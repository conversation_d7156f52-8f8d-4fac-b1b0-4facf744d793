<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
class WMSBilling extends CI_Controller {
	protected $main;
	protected $mybiz_db;
	protected $wms_active_db;
	protected $wms_lp;
	protected $wms_pq;
	protected $wms_qc;
	protected $api_code;
	protected $api_key;
	protected $mcdb;
	public function __construct(){
		parent::__construct();
			$this->load->model('mybiz');
			$this->main = $this->load->database('default', TRUE);
			$this->mybiz_db = $this->load->database('database2', TRUE);
			$this->mcdb = $this->load->database('MC_DB', TRUE);
			$this->active_user_number = $this->session->userdata('uin');
			$this->active_user_info_number = $this->session->userdata('uInfo');
			$this->active_user_info_type = $this->session->userdata('uType');
			$this->active_user_ccode = $this->session->userdata('ui_company_code');
			$this->active_user_ctype = $this->session->userdata('ui_company_type');
			$this->active_user_btype = $this->session->userdata('ui_branch_type');
			$this->active_user_bcode = $this->session->userdata('ui_branch_code');
			$this->active_user_bname = $this->session->userdata('ui_branch_name');
			$this->CIN = $this->session->userdata("CIN");
			$this->CBN = $this->session->userdata("CBN");
			//$this->wms_lp = $this->load->database('wms_lp', TRUE);
			//$this->wms_pq = $this->load->database('wms_pq', TRUE);
			//$this->wms_qc = $this->load->database('wms_qc', TRUE);
			$acc_code = isset($_REQUEST['acc_code']) ? $_REQUEST['acc_code'] : "0";
			
			$hubcode = isset($_REQUEST['acc_hub']) ? $_REQUEST['acc_hub'] : "0";
			switch($hubcode){
				case 'LPHUB':{
					$this->fac_id = 'LP';
					$this->wms_db = $this->load->database('wms_lp', TRUE);
					break;
				};
				case 'PQHUB':{
					$this->fac_id = 'PQ';
					$this->wms_db = $this->load->database('wms_lp', TRUE);
					break;
				};
				case 'QCHUB':{
					$this->fac_id = 'QC';
					$this->wms_db = $this->load->database('wms_lp', TRUE);
					break;
				};
				default:{
					$this->fac_id = 'LP';
					$this->wms_db = $this->load->database('wms_lp', TRUE);
				}
			}
	}
	public function index(){
		echo "ACCESS DENIED";
	}



	public function createBilling()
{
    
    $account = isset($_REQUEST['client_id']) ? $_REQUEST['client_id'] : '';
    $bill_date = isset($_REQUEST['bill_date']) ? $_REQUEST['bill_date'] : '';
    $cutoff_date = isset($_REQUEST['cutoff_date']) ? $_REQUEST['cutoff_date'] : '';
    $service_filter = isset($_REQUEST['service_filter']) ? $_REQUEST['service_filter'] : '';
   
    $errors = [];
    if (empty($account)) {
        $errors[] = 'Account is required.';
    }
    if (empty($cutoff_date)) {
        $errors[] = 'Cutoff date is required.';
    }
    if (!empty($errors)) {
        echo json_encode(['error' => $errors]);
        return;
    }

    $serviceQuery = "
        SELECT cs.service_id, cs.service_name, cs.unit_description,
            sr.rate_per_unit AS rate, sr.minimum_qty
        FROM web_wms_client_services cs
        JOIN web_wms_client_services_billing sr ON cs.service_id = sr.service_id
        WHERE sr.account = ?
    ";

    
    if (!empty($service_filter)) {
        $serviceQuery .= " AND cs.service_id IN ?";
        $serviceResult = $this->mybiz_db->query($serviceQuery, [$account, $service_filter]);
    } else {
        $serviceResult = $this->mybiz_db->query($serviceQuery, [$account]);
    }

    if (!$serviceResult) {
        echo json_encode(['error' => 'Database query failed.', 'details' => $this->mybiz_db->error()]);
        return;
    }

    $serviceData = [];
    if ($serviceResult->num_rows() > 0) {
        foreach ($serviceResult->result() as $row) {
            $min_qty = ($row->minimum_qty > 0) ? $row->minimum_qty : 0;

            $serviceData[$row->service_id] = [
                'service_id' => $row->service_id,
                'service_name' => $row->service_name,
                'unit_description' => $row->unit_description,
                'rate' => $row->rate,
                'minimum_qty' => $min_qty,
                'billable_count' => 0,
                'billAmount' => 0,
                'ids' => ''
            ];
        }
    } else {
        echo json_encode(['message' => 'No services found for the provided account.']);
        return;
    }

    
    // --- STORAGE: Exclude already billed ---
    $getIdStorageQuery = "
        SELECT 
            bdu_id
        FROM df_bin_daily_utilization 
        WHERE account = ? 
        AND bdu_date <= ?
        AND bdu_bin_usage_billable > 200
        AND storage_servicetypeid IS NULL
    ";
    $getIdStorageResult = $this->wms_db->query($getIdStorageQuery, [$account, $cutoff_date]);

    $bduIds = [];
    if ($getIdStorageResult && $getIdStorageResult->num_rows() > 0) {
        foreach ($getIdStorageResult->result() as $row) {
            $bduIds[] = $row->bdu_id;
        }
    }

    $utilQuery = "
        SELECT SUM(bdu_bin_usage_billable - 200) AS total_billable_usage
        FROM df_bin_daily_utilization
        WHERE account = ? AND bdu_date <= ? AND bdu_bin_usage_billable > 200
        AND storage_servicetypeid IS NULL
    ";

    $utilResult = $this->wms_db->query($utilQuery, [$account, $cutoff_date]);

    if (!$utilResult) {
        echo json_encode(['error' => 'Database query failed.', 'details' => $this->wms_db->error()]);
        return;
    }

    $totalBillableUsage = 0;
    if ($utilResult->num_rows() > 0) {
        $row = $utilResult->row();
        $totalBillableUsage = $row->total_billable_usage > 0 ? $row->total_billable_usage : 0;
    }

    

    $storageServiceId = 2; 
    if (isset($serviceData[$storageServiceId])) {
        $serviceData[$storageServiceId]['billable_count'] = $totalBillableUsage;
        $serviceData[$storageServiceId]['billAmount'] = $totalBillableUsage * $serviceData[$storageServiceId]['rate'];
        $serviceData[$storageServiceId]['ids'] = $bduIds;
    }

    
    // --- HANDLING, MANPOWER, OVERTIME: Exclude already billed ---
    // --- HANDLING IN AND OUT ---
    $handlingIds = [];
    $handlingQuery = "
        SELECT rcvno
        FROM tf_rcv0 
        WHERE account = ? 
        AND facid = 'PQ' 
        AND TRIM(rcvfrom) NOT IN ('BIN-TRANSFER', 'RECON', 'CONVERTED') 
        AND rcvdate <= ?
          AND pallets_count > 0
          AND handlingin_servicetypeid IS NULL
          AND handlingout_servicetypeid IS NULL
    ";
    $handlingResult = $this->wms_db->query($handlingQuery, [$account, $cutoff_date]);
    if ($handlingResult && $handlingResult->num_rows() > 0) {
        foreach ($handlingResult->result() as $row) {
            $handlingIds[] = $row->rcvno;
        }
    }

    // --- MANPOWER ---
    $manpowerIds = [];
    $manpowerQuery = "
        SELECT rcvno
        FROM tf_rcv0
        WHERE account = ?
          AND facid = 'PQ'
          AND TRIM(rcvfrom) NOT IN ('BIN-TRANSFER', 'RECON', 'CONVERTED')
          AND rcvdate <= ?
          AND manpower_count > 0
          AND manpower_servicetypeid IS NULL
    ";
    $manpowerResult = $this->wms_db->query($manpowerQuery, [$account, $cutoff_date]);
    if ($manpowerResult && $manpowerResult->num_rows() > 0) {
        foreach ($manpowerResult->result() as $row) {
                $manpowerIds[] = $row->rcvno;
        }
    }

    // --- OVERTIME REGULAR DAY ---
    $overtime_regular_ids = [];
    $overtimeRegularIdQuery = "
        SELECT rcvno
        FROM tf_rcv0
        WHERE account = ? AND facid = 'PQ'
          AND TRIM(rcvfrom) NOT IN ('BIN-TRANSFER', 'RECON', 'CONVERTED')
          AND rcvdate <= ?
          AND day_type = 0 AND overtime_count > 0
          AND otregularday_servicetypeid IS NULL
    ";
    $result = $this->wms_db->query($overtimeRegularIdQuery, [$account, $cutoff_date]);
    if ($result && $result->num_rows() > 0) {
        foreach ($result->result() as $row) {
                $overtime_regular_ids[] = $row->rcvno;
        }
    }

    // --- OVERTIME REGULAR HOLIDAY (day_type = 1) ---
    $overtime_day1_ids = [];
    $overtimeDay1IdQuery = "
        SELECT rcvno
        FROM tf_rcv0
        WHERE account = ? AND facid = 'PQ'
          AND TRIM(rcvfrom) NOT IN ('BIN-TRANSFER', 'RECON', 'CONVERTED')
          AND rcvdate <= ?
          AND day_type = 1 AND overtime_count > 0
          AND otregularholiday_servicetypeid IS NULL
    ";
    $result = $this->wms_db->query($overtimeDay1IdQuery, [$account, $cutoff_date]);
    if ($result && $result->num_rows() > 0) {
        foreach ($result->result() as $row) {
                $overtime_day1_ids[] = $row->rcvno;
        }
    }

    // --- OVERTIME SPECIAL HOLIDAY (day_type = 2) ---
    $overtime_day2_ids = [];
    $overtimeDay2IdQuery = "
        SELECT rcvno
        FROM tf_rcv0
        WHERE account = ? AND facid = 'PQ'
          AND TRIM(rcvfrom) NOT IN ('BIN-TRANSFER', 'RECON', 'CONVERTED')
          AND rcvdate <= ?
          AND day_type = 2 AND overtime_count > 0
          AND otspecialholiday_servicetypeid IS NULL
    ";
    $result = $this->wms_db->query($overtimeDay2IdQuery, [$account, $cutoff_date]);
    if ($result && $result->num_rows() > 0) {
        foreach ($result->result() as $row) {
                $overtime_day2_ids[] = $row->rcvno;
        }
    }

    // --- OVERTIME SUNDAY (day_type = 3) ---
    $overtime_day3_ids = [];
    $overtimeDay3IdQuery = "
        SELECT rcvno
        FROM tf_rcv0
        WHERE account = ? AND facid = 'PQ'
          AND TRIM(rcvfrom) NOT IN ('BIN-TRANSFER', 'RECON', 'CONVERTED')
          AND rcvdate <= ?
          AND day_type = 3 AND overtime_count > 0
          AND otsunday_servicetypeid IS NULL
    ";
    $result = $this->wms_db->query($overtimeDay3IdQuery, [$account, $cutoff_date]);
    if ($result && $result->num_rows() > 0) {
        foreach ($result->result() as $row) {
                $overtime_day3_ids[] = $row->rcvno;
        }
    }

    $handlingQuery = "
        SELECT
            SUM(CASE WHEN tr0.pallets_count > 0 AND tr0.handlingin_servicetypeid IS NULL AND tr0.handlingout_servicetypeid IS NULL THEN tr0.pallets_count ELSE 0 END) AS pallets_count_s,
            SUM(CASE WHEN tr0.manpower_count > 0 AND tr0.manpower_servicetypeid IS NULL THEN tr0.manpower_count ELSE 0 END) AS manpower_count_s,
            SUM(CASE WHEN tr0.day_type = 0 AND tr0.overtime_count > 0 AND tr0.otregularday_servicetypeid IS NULL THEN tr0.overtime_count ELSE 0 END) AS overtime_regular,
            SUM(CASE WHEN tr0.day_type = 1 AND tr0.overtime_count > 0 AND tr0.otregularholiday_servicetypeid IS NULL THEN tr0.overtime_count ELSE 0 END) AS overtime_day1,
            SUM(CASE WHEN tr0.day_type = 2 AND tr0.overtime_count > 0 AND tr0.otspecialholiday_servicetypeid IS NULL THEN tr0.overtime_count ELSE 0 END) AS overtime_day2,
            SUM(CASE WHEN tr0.day_type = 3 AND tr0.overtime_count > 0 AND tr0.otsunday_servicetypeid IS NULL THEN tr0.overtime_count ELSE 0 END) AS overtime_day3
        FROM tf_rcv0 AS tr0
        WHERE tr0.account = ?
            AND tr0.facid = 'PQ'
            AND TRIM(tr0.rcvfrom) NOT IN ('BIN-TRANSFER', 'RECON', 'CONVERTED')
            AND tr0.rcvdate <= ?
    ";

    $handlingResult = $this->wms_db->query($handlingQuery, [$account, $cutoff_date]);

    if (!$handlingResult) {
        echo json_encode(['error' => 'Database query failed.', 'details' => $this->mybiz_db->error()]);
        return;
    }

    if ($handlingResult->num_rows() > 0) {
        $handlingData = $handlingResult->row();
        $palletsCount = $handlingData->pallets_count_s > 0 ? $handlingData->pallets_count_s : 0;
        $manpowerCount = $handlingData->manpower_count_s > 0 ? $handlingData->manpower_count_s : 0;

        
        $overtimeRegular = $handlingData->overtime_regular > 0 ? $handlingData->overtime_regular : 0;
        $overtimeDay1 = $handlingData->overtime_day1 > 0 ? $handlingData->overtime_day1 : 0;
        $overtimeDay2 = $handlingData->overtime_day2 > 0 ? $handlingData->overtime_day2 : 0;
        $overtimeDay3 = $handlingData->overtime_day3 > 0 ? $handlingData->overtime_day3 : 0;

        // Handling service
        $handlingServiceId = 13;
        if (isset($serviceData[$handlingServiceId])) {
            $serviceData[$handlingServiceId]['billable_count'] = $palletsCount * 2;
            $serviceData[$handlingServiceId]['billAmount'] = $palletsCount * 2 * $serviceData[$handlingServiceId]['rate'];
            $serviceData[$handlingServiceId]['ids'] = $handlingIds;
        }

        // Manpower service
        $manpowerServiceId = 10;
        if (isset($serviceData[$manpowerServiceId])) {
            $serviceData[$manpowerServiceId]['billable_count'] = $manpowerCount;
            $serviceData[$manpowerServiceId]['billAmount'] = $manpowerCount * $serviceData[$manpowerServiceId]['rate'];
            $serviceData[$manpowerServiceId]['ids'] = $manpowerIds;
        }

        // Regular overtime (day_type is 0)
        $overtimeServiceId = 1;
        if (isset($serviceData[$overtimeServiceId])) {
            $serviceData[$overtimeServiceId]['billable_count'] = $overtimeRegular;
            $serviceData[$overtimeServiceId]['billAmount'] = $overtimeRegular * $serviceData[$overtimeServiceId]['rate'];
            $serviceData[$overtimeServiceId]['ids'] = $overtime_regular_ids;
        }

        // Overtime services by day_type
        $overtimeMappings = [
            1 => 16,  // day_type 1 -> service_id 16
            2 => 17,  // day_type 2 -> service_id 17
            3 => 18   // day_type 3 -> service_id 18
        ];

        foreach ($overtimeMappings as $dayType => $serviceId) {
            $count = ${"overtimeDay".$dayType};
            if (isset($serviceData[$serviceId])) {
                $serviceData[$serviceId]['billable_count'] = $count;
                $serviceData[$serviceId]['billAmount'] = $count * $serviceData[$serviceId]['rate'];
                $serviceData[$serviceId]['ids'] = ${"overtime_day".$dayType."_ids"};
            }
        }
    }

    // Fetch data for pickpack service
    $pickpackIdQuery = "
        SELECT widno AS pickpack_ids
        FROM tf_orders
        WHERE account = ? 
        AND orderdate <= ? 
        AND (releasing = 'FOR PICK-UP' OR releasing = 'FOR DELIVERY')
        AND pickpack_servicetypeid IS NULL
    ";
    $pickpackIdResult = $this->wms_db->query($pickpackIdQuery, [$account, $cutoff_date]);

    $pickpackQuery = "
        SELECT GROUP_CONCAT(orderno SEPARATOR '/') AS all_orders
        FROM tf_orders
        WHERE account = ? AND orderdate <= ? AND (releasing = 'FOR PICK-UP' OR releasing = 'FOR DELIVERY')
        AND pickpack_servicetypeid IS NULL
    ";

    $pickpackResult = $this->wms_db->query($pickpackQuery, [$account, $cutoff_date]);

    if (!$pickpackResult) {
        echo json_encode(['error' => 'Database query failed.', 'details' => $this->mybiz_db->error()]);
        return;
    }

    $orderCount = 0;

    if ($pickpackResult->num_rows() > 0) {
        $pickpackData = $pickpackResult->row();
        $allOrders = $pickpackData->all_orders ? $pickpackData->all_orders : '';

        $uniqueOrders = array_filter(array_unique(explode('/', $allOrders)));

        $orderCount = count($uniqueOrders);

        // Fetch pickpack IDs
        $pickpackIds = [];
        if ($pickpackIdResult && $pickpackIdResult->num_rows() > 0) {
            foreach ($pickpackIdResult->result() as $row) {
                $pickpackIds[] = $row->pickpack_ids;
            }
        }

        $pickpackServiceId = 9; 
        if (isset($serviceData[$pickpackServiceId])) {
            $serviceData[$pickpackServiceId]['billable_count'] = $orderCount;
            $serviceData[$pickpackServiceId]['billAmount'] = $orderCount * $serviceData[$pickpackServiceId]['rate'];
            $serviceData[$pickpackServiceId]['ids'] = $pickpackIds;
        }
    }

    // --- Add Stuffing and Stripping IDs ---
    // Stuffing 1x20 and 1x40 (tf_orders, widno)
    $stuffingIdQuery = "
        SELECT widno, consize
        FROM tf_orders
        WHERE account = ? AND orderdate <= ? AND (consize = '20' OR consize = '40')
        AND ((consize = '20' AND stuffing1x20_servicetypeid IS NULL)
             OR (consize = '40' AND stuffing1x40_servicetypeid IS NULL))
    ";
    $stuffingIdResult = $this->wms_db->query($stuffingIdQuery, [$account, $cutoff_date]);
    $stuffing1x20Ids = [];
    $stuffing1x40Ids = [];
    if ($stuffingIdResult && $stuffingIdResult->num_rows() > 0) {
        foreach ($stuffingIdResult->result() as $row) {
            if ($row->consize == '20') {
                $stuffing1x20Ids[] = $row->widno;
            } elseif ($row->consize == '40') {
                $stuffing1x40Ids[] = $row->widno;
            }
        }
    }
    $stuffing1x20ServiceId = 4;
    $stuffing1x40ServiceId = 12;
    if (isset($serviceData[$stuffing1x20ServiceId])) {
        $serviceData[$stuffing1x20ServiceId]['ids'] = $stuffing1x20Ids;
    }
    if (isset($serviceData[$stuffing1x40ServiceId])) {
        $serviceData[$stuffing1x40ServiceId]['ids'] = $stuffing1x40Ids;
    }

    // Stripping 1x20 and 1x40 (tf_rcv0, rcvno)
    $strippingIdQuery = "
        SELECT rcvno, consize
        FROM tf_rcv0
        WHERE account = ? AND rcvdate <= ? AND (consize = '20' OR consize = '40')
        AND ((consize = '20' AND stripping1x20_servicetypeid IS NULL)
             OR (consize = '40' AND stripping1x40_servicetypeid IS NULL))
    ";
    $strippingIdResult = $this->wms_db->query($strippingIdQuery, [$account, $cutoff_date]);
    $stripping1x20Ids = [];
    $stripping1x40Ids = [];
    if ($strippingIdResult && $strippingIdResult->num_rows() > 0) {
        foreach ($strippingIdResult->result() as $row) {
            if ($row->consize == '20') {
                $stripping1x20Ids[] = $row->rcvno;
            } elseif ($row->consize == '40') {
                $stripping1x40Ids[] = $row->rcvno;
            }
        }
    }
    $stripping1x20ServiceId = 14;
    $stripping1x40ServiceId = 15;
    if (isset($serviceData[$stripping1x20ServiceId])) {
        $serviceData[$stripping1x20ServiceId]['ids'] = $stripping1x20Ids;
    }
    if (isset($serviceData[$stripping1x40ServiceId])) {
        $serviceData[$stripping1x40ServiceId]['ids'] = $stripping1x40Ids;
    }

    // Insurance billing
    $pickpackServiceId = 11; 
    if (isset($serviceData[$pickpackServiceId])) {
        $serviceData[$pickpackServiceId]['billAmount'] = $serviceData[$pickpackServiceId]['rate'];
    }

    
    $stuffingQuery = "
        SELECT 
            SUM(CASE WHEN consize = '20' THEN 1 ELSE 0 END) AS count_1x20,
            SUM(CASE WHEN consize = '40' THEN 1 ELSE 0 END) AS count_1x40
        FROM tf_orders
        WHERE account = ? AND orderdate <= ?
    ";

    $stuffingResult = $this->wms_db->query($stuffingQuery, [$account, $cutoff_date]);

    if (!$stuffingResult) {
        echo json_encode(['error' => 'Database query failed.', 'details' => $this->wms_db->error()]);
        return;
    }

    if ($stuffingResult->num_rows() > 0) {
        $stuffingData = $stuffingResult->row();
        $count1x20 = $stuffingData->count_1x20 > 0 ? $stuffingData->count_1x20 : 0;
        $count1x40 = $stuffingData->count_1x40 > 0 ? $stuffingData->count_1x40 : 0;

        $stuffing1x20ServiceId = 4;
        if (isset($serviceData[$stuffing1x20ServiceId])) {
            $serviceData[$stuffing1x20ServiceId]['billable_count'] = $count1x20;
            $serviceData[$stuffing1x20ServiceId]['billAmount'] = $count1x20 * $serviceData[$stuffing1x20ServiceId]['rate'];
        }

        $stuffing1x40ServiceId = 12;
        if (isset($serviceData[$stuffing1x40ServiceId])) {
            $serviceData[$stuffing1x40ServiceId]['billable_count'] = $count1x40;
            $serviceData[$stuffing1x40ServiceId]['billAmount'] = $count1x40 * $serviceData[$stuffing1x40ServiceId]['rate'];
        }
    }

    
    $strippingQuery = "
        SELECT 
            SUM(CASE WHEN consize = '20' THEN 1 ELSE 0 END) AS count_1x20,
            SUM(CASE WHEN consize = '40' THEN 1 ELSE 0 END) AS count_1x40
        FROM tf_rcv0
        WHERE account = ? AND rcvdate <= ?
    ";

    $strippingResult = $this->wms_db->query($strippingQuery, [$account, $cutoff_date]);

    if (!$strippingResult) {
        echo json_encode(['error' => 'Database query failed.', 'details' => $this->wms_db->error()]);
        return;
    }

    if ($strippingResult->num_rows() > 0) {
        $strippingData = $strippingResult->row();
        $stripping_count1x20 = $strippingData->count_1x20 > 0 ? $strippingData->count_1x20 : 0;
        $stripping_count1x40 = $strippingData->count_1x40 > 0 ? $strippingData->count_1x40 : 0;

        $stripping1x20ServiceId = 14;
        if (isset($serviceData[$stripping1x20ServiceId])) {
            $serviceData[$stripping1x20ServiceId]['billable_count'] = $stripping_count1x20;
            $serviceData[$stripping1x20ServiceId]['billAmount'] = $stripping_count1x20 * $serviceData[$stripping1x20ServiceId]['rate'];
        }

        $stripping1x40ServiceId = 15;
        if (isset($serviceData[$stripping1x40ServiceId])) {
            $serviceData[$stripping1x40ServiceId]['billable_count'] = $stripping_count1x40;
            $serviceData[$stripping1x40ServiceId]['billAmount'] = $stripping_count1x40 * $serviceData[$stripping1x40ServiceId]['rate'];
        }
    }

    // --- PULL-OUT MANPOWER ---
    // Find the service_id for Pull-out Manpower if present
    $pulloutManpowerServiceId = null;
    foreach ($serviceData as $service) {
        if (strpos($service['service_name'], 'Pull-out Manpower') !== false) {
            $pulloutManpowerServiceId = $service['service_id'];
        }
    }
    if ($pulloutManpowerServiceId && isset($serviceData[$pulloutManpowerServiceId])) {
        // Query tf_orders for pull-out manpower
        $pulloutManpowerQuery = "SELECT widno, manpower_count FROM tf_orders WHERE account = ? AND orderdate <= ? AND manpower_count > 0 AND pullout_manpower_servicetypeid IS NULL";
        $pulloutManpowerResult = $this->wms_db->query($pulloutManpowerQuery, [$account, $cutoff_date]);
        $pulloutManpowerIds = [];
        $pulloutManpowerCount = 0;
        if ($pulloutManpowerResult && $pulloutManpowerResult->num_rows() > 0) {
            foreach ($pulloutManpowerResult->result() as $row) {
                $pulloutManpowerIds[] = $row->widno;
                $pulloutManpowerCount += (int)$row->manpower_count;
            }
        }
        $serviceData[$pulloutManpowerServiceId]['billable_count'] = $pulloutManpowerCount;
        $serviceData[$pulloutManpowerServiceId]['billAmount'] = $pulloutManpowerCount * $serviceData[$pulloutManpowerServiceId]['rate'];
        $serviceData[$pulloutManpowerServiceId]['ids'] = $pulloutManpowerIds;
    }

    // Calculate the total bill for all services
    $totalBill = 0;
    foreach ($serviceData as $service) {
        $totalBill += $service['billAmount'];
    }

    // Prepare response data
    $responseData = [
        'serviceData' => $serviceData,
        'totalBill' => $totalBill
    ];

    echo json_encode($responseData);
}

// /lwli/index.php/WMSBilling/saveBilling
public function saveBilling() {
    $data = json_decode(file_get_contents('php://input'), true);

    // Extract data
    $client_id = $data['client_id'];
    $bill_date = $data['bill_date'];
    $cutoff_date = $data['cutoff_date'];
    $created_by = $data['created_by'];
    $invoice_num = $data['invoice_num'];
    $tin_no = $data['tin_no'];
    $totalAmount = $data['totalAmount'];
    $services = $data['services'];

    // Only allow new billing creation. Remove edit mode logic.
    $billingData = [
        'account' => $client_id,
        'bill_date' => $bill_date,
        'billvt_refnum' => $invoice_num,
        'encodedby' => $created_by,
        'tinnum' => $tin_no,
        'bill_type' => '2',
        'cutoff_period' => $cutoff_date,
        'totalamount' => $totalAmount
    ];

    $insertSuccess = $this->mybiz_db->insert('web_bill_accounts', $billingData);

    if ($insertSuccess) {
        $billing_id = $this->mybiz_db->insert_id();
        
        if (!$billing_id) {
            echo json_encode(['success' => false, 'message' => 'Failed to retrieve billing ID.']);
            return;
        }

        // Save services to web_bill_warehouse
        foreach ($services as $service) {
            if($service['billAmount'] > 0 ){
                    $warehouseData = [
                        'bill_magic' => $billing_id,
                        'service_name' => $service['service_name'],
                        'unit_description' => $service['unit_description'],
                        'billable_qty' => $service['billable_count'],
                        'rate' => $service['rate'],
                        'bill_amount' => $service['billAmount'],
                        'service_id' => $service['service_id']
                    ];
                $this->mybiz_db->insert('web_bill_warehouse', $warehouseData);
                $warehouse_id = $this->mybiz_db->insert_id();
                $service['service_id'] = $warehouse_id;
                $this->updateIsBilled($client_id, $cutoff_date, $service);
            }
        }

        echo json_encode(['success' => true, 'message' => 'Billing saved successfully.', 'bill_magic' => $billing_id]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to save billing.']);
    }
}

private function unmarkServiceAsBilled($service_row) {
    $serviceTypeColumnMap = [
        'Stripping 1x40' => ['table' => 'tf_rcv0', 'column' => 'stripping1x40_servicetypeid'],
        'Stripping 1x20' => ['table' => 'tf_rcv0', 'column' => 'stripping1x20_servicetypeid'],
        'Handling In and Handling Out' => ['table' => 'tf_rcv0', 'columns' => ['handlingin_servicetypeid', 'handlingout_servicetypeid']],
        'Receiving Manpower' => ['table' => 'tf_rcv0', 'column' => 'manpower_servicetypeid'],
        'Pull-out Manpower' => ['table' => 'tf_orders', 'column' => 'pullout_manpower_servicetypeid'],
        'Overtime Regular Day' => ['table' => 'tf_rcv0', 'column' => 'otregularday_servicetypeid'],
        'Overtime Regular Holiday' => ['table' => 'tf_rcv0', 'column' => 'otregularholiday_servicetypeid'],
        'Overtime Special Holiday' => ['table' => 'tf_rcv0', 'column' => 'otspecialholiday_servicetypeid'],
        'Overtime Sunday' => ['table' => 'tf_rcv0', 'column' => 'otsunday_servicetypeid'],
        'Storage' => ['table' => 'df_bin_daily_utilization', 'column' => 'storage_servicetypeid'],
        'Pick and Pack' => ['table' => 'tf_orders', 'column' => 'pickpack_servicetypeid'],
        'Stuffing 1x20' => ['table' => 'tf_orders', 'column' => 'stuffing1x20_servicetypeid'],
        'Stuffing 1x40' => ['table' => 'tf_orders', 'column' => 'stuffing1x40_servicetypeid'],
    ];

    $mapping = isset($serviceTypeColumnMap[$service_row['service_name']]) ? $serviceTypeColumnMap[$service_row['service_name']] : null;
    if (!$mapping) {
        return;
    }

    $table = $mapping['table'];
    $db = $this->mybiz_db;
    if (in_array($table, ['df_bin_daily_utilization', 'tf_rcv0', 'tf_orders'])) {
        $db = $this->wms_db;
    }

    if (isset($mapping['columns'])) { // For 'Handling In and Handling Out'
        $updateData = [];
        foreach ($mapping['columns'] as $col) {
            $updateData[$col] = NULL;
        }
        $db->where($mapping['columns'][0], $service_row['id']);
        $db->update($table, $updateData);
    } else {
        $column = $mapping['column'];
        $db->where($column, $service_row['id']);
        $db->update($table, [$column => NULL]);
    }
}

private function updateIsBilled($client_id, $cutoff_date, $service) {
    $serviceTypeColumnMap = [
        'Stripping 1x40' => ['table' => 'tf_rcv0', 'column' => 'stripping1x40_servicetypeid', 'id_col' => 'rcvno'],
        'Stripping 1x20' => ['table' => 'tf_rcv0', 'column' => 'stripping1x20_servicetypeid', 'id_col' => 'rcvno'],
        'Handling In and Handling Out' => ['table' => 'tf_rcv0', 'column' => 'handlingin_servicetypeid', 'id_col' => 'rcvno'],
        'Receiving Manpower' => ['table' => 'tf_rcv0', 'column' => 'manpower_servicetypeid', 'id_col' => 'rcvno'],
        'Pull-out Manpower' => ['table' => 'tf_orders', 'column' => 'pullout_manpower_servicetypeid', 'id_col' => 'widno'],
        'Overtime Regular Day' => ['table' => 'tf_rcv0', 'column' => 'otregularday_servicetypeid', 'id_col' => 'rcvno'],
        'Overtime Regular Holiday' => ['table' => 'tf_rcv0', 'column' => 'otregularholiday_servicetypeid', 'id_col' => 'rcvno'],
        'Overtime Special Holiday' => ['table' => 'tf_rcv0', 'column' => 'otspecialholiday_servicetypeid', 'id_col' => 'rcvno'],
        'Overtime Sunday' => ['table' => 'tf_rcv0', 'column' => 'otsunday_servicetypeid', 'id_col' => 'rcvno'],
        'Storage' => ['table' => 'df_bin_daily_utilization', 'column' => 'storage_servicetypeid', 'id_col' => 'bdu_id'],
        'Pick and Pack' => ['table' => 'tf_orders', 'column' => 'pickpack_servicetypeid', 'id_col' => 'widno'],
        'Stuffing 1x20' => ['table' => 'tf_orders', 'column' => 'stuffing1x20_servicetypeid', 'id_col' => 'widno'],
        'Stuffing 1x40' => ['table' => 'tf_orders', 'column' => 'stuffing1x40_servicetypeid', 'id_col' => 'widno'],
        // Add other mappings as needed
    ];

    $mapping = isset($serviceTypeColumnMap[$service['service_name']]) ? $serviceTypeColumnMap[$service['service_name']] : null;
    if (!$mapping) {
        return; // No mapping found
    }
    $table = $mapping['table'];
    $column = $mapping['column'];
    $id_col = isset($mapping['id_col']) ? $mapping['id_col'] : null;
    $serviceTypeId = isset($service['web_bill_warehouse_id']) ? $service['web_bill_warehouse_id'] : ($service['service_id'] ? $service['service_id'] : null);
    if (!$serviceTypeId) {
        return; // No service id provided
    }
    $condition = '';
    $ids = isset($service['ids']) ? $service['ids'] : array();

    switch ($service['service_name']) {
        case 'Storage':
            if (!empty($ids)) {
                $idList = implode(',', array_map(function($id) { return "'".addslashes($id)."'"; }, $ids));
                $condition = "bdu_id IN ($idList)";
            } else {
                $condition = "account = '$client_id' AND bdu_date <= '$cutoff_date' AND bdu_bin_usage_billable > 200 AND storage_servicetypeid IS NULL";
            }
            break;
        case 'Pull-out Manpower':
            if (!empty($ids)) {
                $idList = implode(',', array_map(function($id) { return "'".addslashes($id)."'"; }, $ids));
                $condition = "widno IN ($idList)";
            } else {
                $condition = "account = '$client_id' AND orderdate <= '$cutoff_date' AND manpower_count > 0 AND pullout_manpower_servicetypeid IS NULL";
            }
            break;
        case 'Handling In and Handling Out':
            if (!empty($ids)) {
                $idList = implode(',', array_map(function($id) { return "'".addslashes($id)."'"; }, $ids));
                $condition = "$id_col IN ($idList)";
            } else {
                $condition = "account = '$client_id' AND rcvdate <= '$cutoff_date' AND facid = 'PQ' AND TRIM(rcvfrom) NOT IN ('BIN-TRANSFER', 'RECON', 'CONVERTED') AND pallets_count > 0 AND handlingin_servicetypeid IS NULL AND handlingout_servicetypeid IS NULL";
            }
            // Update both handlingin_servicetypeid and handlingout_servicetypeid
            $updateQuery = "UPDATE $table SET handlingin_servicetypeid = $serviceTypeId, handlingout_servicetypeid = $serviceTypeId WHERE $condition";
            $db = $this->mybiz_db;
            if (in_array($table, ['df_bin_daily_utilization', 'tf_rcv0', 'tf_orders'])) {
                $db = $this->wms_db;
            }
            $db->query($updateQuery);
            return;
        case 'Receiving Manpower':
            if (!empty($ids)) {
                $idList = implode(',', array_map(function($id) { return "'".addslashes($id)."'"; }, $ids));
                $condition = "$id_col IN ($idList)";
            } else {
                $condition = "account = '$client_id' AND rcvdate <= '$cutoff_date' AND facid = 'PQ' AND TRIM(rcvfrom) NOT IN ('BIN-TRANSFER', 'RECON', 'CONVERTED') AND manpower_count > 0 AND manpower_servicetypeid IS NULL";
            }
            break;
        case 'Overtime Regular Day':
        case 'Overtime Regular Holiday':
        case 'Overtime Special Holiday':
        case 'Overtime Sunday':
        case 'Stripping 1x20':
        case 'Stripping 1x40':
            if (!empty($ids)) {
                $idList = implode(',', array_map(function($id) { return "'".addslashes($id)."'"; }, $ids));
                $condition = "$id_col IN ($idList)";
            } else {
                switch ($service['service_name']) {
                    case 'Receiving Manpower':
                        $condition = "account = '$client_id' AND rcvdate <= '$cutoff_date' AND facid = 'PQ' AND TRIM(rcvfrom) NOT IN ('BIN-TRANSFER', 'RECON', 'CONVERTED') AND manpower_count > 0 AND manpower_servicetypeid IS NULL";
                        break;
                    case 'Overtime Regular Day':
                        $condition = "account = '$client_id' AND rcvdate <= '$cutoff_date' AND facid = 'PQ' AND TRIM(rcvfrom) NOT IN ('BIN-TRANSFER', 'RECON', 'CONVERTED') AND day_type = 0 AND overtime_count > 0 AND otregularday_servicetypeid IS NULL";
                        break;
                    case 'Overtime Regular Holiday':
                        $condition = "account = '$client_id' AND rcvdate <= '$cutoff_date' AND facid = 'PQ' AND TRIM(rcvfrom) NOT IN ('BIN-TRANSFER', 'RECON', 'CONVERTED') AND day_type = 1 AND overtime_count > 0 AND otregularholiday_servicetypeid IS NULL";
                        break;
                    case 'Overtime Special Holiday':
                        $condition = "account = '$client_id' AND rcvdate <= '$cutoff_date' AND facid = 'PQ' AND TRIM(rcvfrom) NOT IN ('BIN-TRANSFER', 'RECON', 'CONVERTED') AND day_type = 2 AND overtime_count > 0 AND otspecialholiday_servicetypeid IS NULL";
                        break;
                    case 'Overtime Sunday':
                        $condition = "account = '$client_id' AND rcvdate <= '$cutoff_date' AND facid = 'PQ' AND TRIM(rcvfrom) NOT IN ('BIN-TRANSFER', 'RECON', 'CONVERTED') AND day_type = 3 AND overtime_count > 0 AND otsunday_servicetypeid IS NULL";
                        break;
                    case 'Stripping 1x20':
                        $condition = "account = '$client_id' AND rcvdate <= '$cutoff_date' AND consize = '20' AND stripping1x20_servicetypeid IS NULL";
                        break;
                    case 'Stripping 1x40':
                        $condition = "account = '$client_id' AND rcvdate <= '$cutoff_date' AND consize = '40' AND stripping1x40_servicetypeid IS NULL";
                        break;
                }
            }
            break;
        case 'Pick and Pack':
            if (!empty($ids)) {
                $idList = implode(',', array_map(function($id) { return "'".addslashes($id)."'"; }, $ids));
                $condition = "widno IN ($idList)";
            } else {
                $condition = "account = '$client_id' AND orderdate <= '$cutoff_date' AND (releasing = 'FOR PICK-UP' OR releasing = 'FOR DELIVERY') AND pickpack_servicetypeid IS NULL";
            }
            break;
        case 'Stuffing 1x20':
            $condition = "account = '$client_id' AND orderdate <= '$cutoff_date' AND consize = '20' AND stuffing1x20_servicetypeid IS NULL";
            break;
        case 'Stuffing 1x40':
            $condition = "account = '$client_id' AND orderdate <= '$cutoff_date' AND consize = '40' AND stuffing1x40_servicetypeid IS NULL";
            break;
        case 'Pull-out Manpower':
            $condition = "account = '$client_id' AND orderdate <= '$cutoff_date' AND manpower_count > 0 AND pullout_manpower_servicetypeid IS NULL";
            break;
        default:
            return;
    }

    $updateQuery = "UPDATE $table SET $column = $serviceTypeId WHERE $condition";
    // Choose the correct DB connection
    $db = $this->mybiz_db;
    if (in_array($table, ['df_bin_daily_utilization', 'tf_rcv0', 'tf_orders'])) {
        $db = $this->wms_db;
    }
    $db->query($updateQuery);
}



public function getBillingAttachments($bill_magic) {
    // Fetch main billing details from web_bill_accounts
    $this->mybiz_db->select('account, bill_date, cutoff_period, billvt_refnum, tinnum');
    $this->mybiz_db->where('bill_magic', $bill_magic);
    $billing_result = $this->mybiz_db->get('web_bill_accounts');
    $billing_data = $billing_result->row_array();

    if (empty($billing_data)) {
        echo json_encode(['error' => 'Billing record not found.']);
        return;
    }

    // Fetch associated services from web_bill_warehouse
    $this->mybiz_db->select('service_name, unit_description, billable_qty, rate, bill_amount, id');
    $this->mybiz_db->where('bill_magic', $bill_magic);
    $services_result = $this->mybiz_db->get('web_bill_warehouse');
    $services_data = $services_result->result_array();

    $attachments = [];
    $account = $billing_data['account'];
    $cutoff_date = $billing_data['cutoff_period'];

    foreach ($services_data as $service) {
        $serviceName = $service['service_name'];
        $serviceTypeId = $service['id'];
        
        $sourceData = $this->getSourceDataForService($serviceName, $account, $cutoff_date, $serviceTypeId);
        
        if (!empty($sourceData)) {
            $attachments[$serviceName] = [
                'service_info' => $service,
                'source_data' => $sourceData
            ];
        }
    }

    echo json_encode([
        'billing_info' => $billing_data,
        'attachments' => $attachments
    ]);
}

private function getSourceDataForService($serviceName, $account, $cutoff_date, $serviceTypeId) {
    $sourceData = [];
    
    switch ($serviceName) {
        case 'Storage':
            // Get storage data from df_bin_daily_utilization
            $query = "SELECT bdu_date, bdu_bin_usage_billable FROM df_bin_daily_utilization WHERE account = ? AND bdu_date <= ? AND bdu_bin_usage_billable > 200 AND storage_servicetypeid = ? ORDER BY bdu_date ASC";
            $result = $this->wms_db->query($query, [$account, $cutoff_date, $serviceTypeId]);
            // Fetch the dynamic rate from web_bill_warehouse
            $rate = 0;
            $rateQuery = $this->mybiz_db->query("SELECT rate FROM web_bill_warehouse WHERE id = ?", [$serviceTypeId]);
            if ($rateQuery && $rateQuery->num_rows() > 0) {
                $rate = (float)$rateQuery->row()->rate;
            }
            $allocation = 200;
            $days = [];
            $usage = [];
            $excess = [];
            $amount_due = [];
            $total = 0;
            if ($result && $result->num_rows() > 0) {
                foreach ($result->result_array() as $row) {
                    $date = $row['bdu_date'];
                    $days[] = $date;
                    $usage[$date] = $row['bdu_bin_usage_billable'];
                    $excess[$date] = max(0, $row['bdu_bin_usage_billable'] - $allocation);
                    $amount_due[$date] = $excess[$date] * $rate;
                    $total += $amount_due[$date];
                }
            }
            $vat = $total * 0.12;
            $grand_total = $total + $vat;
            $sourceData = [
                'days' => $days,
                'allocation' => $allocation,
                'usage' => $usage,
                'excess' => $excess,
                'rate' => $rate,
                'amount_due' => $amount_due,
                'total' => $total,
                'vat' => $vat,
                'grand_total' => $grand_total
            ];
            break;
            
        case 'Handling In and Handling Out':
            // Get handling data from tf_rcv0
            $query = "SELECT rcvno, rcvdate, rcvfrom, pallets_count as pallet_count
                      FROM tf_rcv0 
                      WHERE account = ? 
                      AND rcvdate <= ? 
                      AND facid = 'PQ' 
                      AND TRIM(rcvfrom) NOT IN ('BIN-TRANSFER', 'RECON', 'CONVERTED') 
                      AND pallets_count > 0
                      AND (handlingin_servicetypeid = ? OR handlingout_servicetypeid = ?)";
            $result = $this->wms_db->query($query, [$account, $cutoff_date, $serviceTypeId, $serviceTypeId]);
            if ($result && $result->num_rows() > 0) {
                $sourceData = $result->result_array();
            }
            break;
            
        case 'Receiving Manpower':
            // Get manpower data from tf_rcv0
            $query = "SELECT rcvno, rcvdate, rcvfrom, manpower_count
                      FROM tf_rcv0 
                      WHERE account = ? 
                      AND rcvdate <= ? 
                      AND facid = 'PQ' 
                      AND TRIM(rcvfrom) NOT IN ('BIN-TRANSFER', 'RECON', 'CONVERTED') 
                      AND manpower_count > 0
                      AND manpower_servicetypeid = ?";
            $result = $this->wms_db->query($query, [$account, $cutoff_date, $serviceTypeId]);
            if ($result && $result->num_rows() > 0) {
                $sourceData = $result->result_array();
            }
            break;
        
        case 'Pull-out Manpower':
            $query = "SELECT widno, orderdate, consignee, manpower_count
                      FROM tf_orders 
                      WHERE account = ? 
                      AND orderdate <= ? 
                      AND manpower_count > 0
                      AND pullout_manpower_servicetypeid = ?";
            $result = $this->wms_db->query($query, [$account, $cutoff_date, $serviceTypeId]);
            if ($result && $result->num_rows() > 0) {
                $sourceData = $result->result_array();
            }
            break;
            
        case 'Overtime Regular Day':
            // Get overtime regular data from tf_rcv0
            $query = "SELECT rcvno, rcvdate, rcvfrom, overtime_count, day_type, consize
                      FROM tf_rcv0 
                      WHERE account = ? 
                      AND rcvdate <= ? 
                      AND facid = 'PQ' 
                      AND TRIM(rcvfrom) NOT IN ('BIN-TRANSFER', 'RECON', 'CONVERTED') 
                      AND day_type = 0 
                      AND overtime_count > 0
                      AND otregularday_servicetypeid = ?";
            $result = $this->wms_db->query($query, [$account, $cutoff_date, $serviceTypeId]);
            if ($result && $result->num_rows() > 0) {
                $sourceData = $result->result_array();
            }
            break;
            
        case 'Overtime Regular Holiday':
            // Get overtime regular holiday data from tf_rcv0
            $query = "SELECT rcvno, rcvdate, rcvfrom, overtime_count, day_type, consize
                      FROM tf_rcv0 
                      WHERE account = ? 
                      AND rcvdate <= ? 
                      AND facid = 'PQ' 
                      AND TRIM(rcvfrom) NOT IN ('BIN-TRANSFER', 'RECON', 'CONVERTED') 
                      AND day_type = 1 
                      AND overtime_count > 0
                      AND otregularholiday_servicetypeid = ?";
            $result = $this->wms_db->query($query, [$account, $cutoff_date, $serviceTypeId]);
            if ($result && $result->num_rows() > 0) {
                $sourceData = $result->result_array();
            }
            break;
            
        case 'Overtime Special Holiday':
            // Get overtime special holiday data from tf_rcv0
            $query = "SELECT rcvno, rcvdate, rcvfrom, overtime_count, day_type, consize
                      FROM tf_rcv0 
                      WHERE account = ? 
                      AND rcvdate <= ? 
                      AND facid = 'PQ' 
                      AND TRIM(rcvfrom) NOT IN ('BIN-TRANSFER', 'RECON', 'CONVERTED') 
                      AND day_type = 2 
                      AND overtime_count > 0
                      AND otspecialholiday_servicetypeid = ?";
            $result = $this->wms_db->query($query, [$account, $cutoff_date, $serviceTypeId]);
            if ($result && $result->num_rows() > 0) {
                $sourceData = $result->result_array();
            }
            break;
            
        case 'Overtime Sunday':
            // Get overtime sunday data from tf_rcv0
            $query = "SELECT rcvno, rcvdate, rcvfrom, overtime_count, day_type, consize
                      FROM tf_rcv0 
                      WHERE account = ? 
                      AND rcvdate <= ? 
                      AND facid = 'PQ' 
                      AND TRIM(rcvfrom) NOT IN ('BIN-TRANSFER', 'RECON', 'CONVERTED') 
                      AND day_type = 3 
                      AND overtime_count > 0
                      AND otsunday_servicetypeid = ?";
            $result = $this->wms_db->query($query, [$account, $cutoff_date, $serviceTypeId]);
            if ($result && $result->num_rows() > 0) {
                $sourceData = $result->result_array();
            }
            break;
            
        case 'Pick and Pack':
            // Get pick and pack data from tf_orders and tf_wid1
            $query = "SELECT o.widno AS number, o.orderdate AS date, o.orderno AS dn_number, o.consignee AS name, w.prodcode AS item_code, w.lottable1 AS batch, w.orderqty AS quantity
                      FROM tf_orders o
                      LEFT JOIN tf_wid1 w ON o.widno = w.widno
                      WHERE o.account = ? 
                      AND o.orderdate <= ? 
                      AND (o.releasing = 'FOR PICK-UP' OR o.releasing = 'FOR DELIVERY')
                      AND o.pickpack_servicetypeid = ?";
            $result = $this->wms_db->query($query, [$account, $cutoff_date, $serviceTypeId]);
            if ($result && $result->num_rows() > 0) {
                $sourceData = $result->result_array();
            }
            break;
            
        case 'Stuffing 1x20':
            // Get stuffing 1x20 data from tf_orders
            $query = "SELECT widno, orderdate, orderno, consize
                      FROM tf_orders 
                      WHERE account = ? 
                      AND orderdate <= ? 
                      AND consize = '20'
                      AND stuffing1x20_servicetypeid = ?";
            $result = $this->wms_db->query($query, [$account, $cutoff_date, $serviceTypeId]);
            if ($result && $result->num_rows() > 0) {
                $sourceData = $result->result_array();
            }
            break;
            
        case 'Stuffing 1x40':
            // Get stuffing 1x40 data from tf_orders
            $query = "SELECT widno, orderdate, orderno, consize
                      FROM tf_orders 
                      WHERE account = ? 
                      AND orderdate <= ? 
                      AND consize = '40'
                      AND stuffing1x40_servicetypeid = ?";
            $result = $this->wms_db->query($query, [$account, $cutoff_date, $serviceTypeId]);
            if ($result && $result->num_rows() > 0) {
                $sourceData = $result->result_array();
            }
            break;
            
        case 'Stripping 1x20':
            // Get stripping 1x20 data from tf_rcv0
            $query = "SELECT rcvno, rcvdate, rcvfrom, consize
                      FROM tf_rcv0 
                      WHERE account = ? 
                      AND rcvdate <= ? 
                      AND consize = '20'
                      AND stripping1x20_servicetypeid = ?";
            $result = $this->wms_db->query($query, [$account, $cutoff_date, $serviceTypeId]);
            if ($result && $result->num_rows() > 0) {
                $sourceData = $result->result_array();
            }
            break;
            
        case 'Stripping 1x40':
            // Get stripping 1x40 data from tf_rcv0
            $query = "SELECT rcvno, rcvdate, rcvfrom, consize
                      FROM tf_rcv0 
                      WHERE account = ? 
                      AND rcvdate <= ? 
                      AND consize = '40'
                      AND stripping1x40_servicetypeid = ?";
            $result = $this->wms_db->query($query, [$account, $cutoff_date, $serviceTypeId]);
            if ($result && $result->num_rows() > 0) {
                $sourceData = $result->result_array();
            }
            break;
    }
    
    return $sourceData;
}

public function cancelBilling($bill_magic) {
    // Check if billing is approved
    $this->mybiz_db->select('isapproved');
    $this->mybiz_db->where('bill_magic', $bill_magic);
    $approved_result = $this->mybiz_db->get('web_bill_accounts');
    if ($approved_result->num_rows() > 0 && $approved_result->row()->isapproved == 1) {
        echo json_encode(['success' => false, 'message' => 'Cannot cancel an approved billing.']);
        return;
    }
    // 1. Mark billing as cancelled
    $this->mybiz_db->where('bill_magic', $bill_magic);
    $update = $this->mybiz_db->update('web_bill_accounts', ['iscancelled' => 1]);
    if (!$update) {
        echo json_encode(['success' => false, 'message' => 'Failed to cancel billing.']);
        return;
    }
    // 2. Unmark all sources as billed
    $this->mybiz_db->select('id, service_name');
    $this->mybiz_db->where('bill_magic', $bill_magic);
    $services = $this->mybiz_db->get('web_bill_warehouse')->result_array();
    
    foreach ($services as $service) {
        $this->unmarkServiceAsBilled($service);
    }

    echo json_encode(['success' => true]);
}

public function approveBilling($bill_magic) {
    // 1. Mark billing as approved
    $this->mybiz_db->where('bill_magic', $bill_magic);
    $update = $this->mybiz_db->update('web_bill_accounts', ['isapproved' => 1]);
    if (!$update) {
        echo json_encode(['success' => false, 'message' => 'Failed to approve billing.']);
        return;
    }
    echo json_encode(['success' => true]);
}

}
?>